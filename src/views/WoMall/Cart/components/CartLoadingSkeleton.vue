<template>
  <section class="cart-loading">
    <!-- 头部地址选择区域骨架屏 -->
    <header v-if="showHeader" class="cart-loading__header">
      <div class="cart-loading__address">
        <div class="cart-loading__location-icon"></div>
        <div class="cart-loading__address-text"></div>
        <div class="cart-loading__arrow-icon"></div>
      </div>
      <div class="cart-loading__edit-btn"></div>
    </header>

    <!-- 商品列表骨架屏 -->
    <WoCard class="cart-loading__item" v-for="n in 2" :key="n">
      <div class="cart-loading__content">
        <div class="cart-loading__checkbox"></div>
        <div class="cart-loading__image"></div>
        <div class="cart-loading__info">
          <div class="cart-loading__goods-header">
            <div class="cart-loading__name"></div>
            <div class="cart-loading__quantity"></div>
          </div>
          <div class="cart-loading__tags">
            <div class="cart-loading__spec-tag"></div>
            <div class="cart-loading__spec-tag"></div>
          </div>
          <div class="cart-loading__price"></div>
        </div>
      </div>
    </WoCard>
  </section>
</template>

<script setup>
import WoCard from '@components/WoElementCom/WoCard.vue'

// 定义组件属性
const props = defineProps({
  showHeader: {
    type: Boolean,
    default: true
  }
})
</script>

<style scoped lang="less">
.cart-loading {
  // 头部骨架屏样式
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    gap: 15px;
  }

  &__address {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
  }

  &__location-icon {
    width: 11px;
    height: 12px;
    margin-right: 5px;
    flex-shrink: 0;
    border-radius: 2px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__address-text {
    flex: 1;
    height: 12px;
    border-radius: 4px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__arrow-icon {
    width: 5px;
    height: 9px;
    margin-left: 5px;
    flex-shrink: 0;
    border-radius: 2px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__edit-btn {
    width: 30px;
    height: 12px;
    flex-shrink: 0;
    border-radius: 4px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  // 商品列表骨架屏样式
  &__item {
    margin-bottom: 10px;
  }

  &__content {
    display: flex;
    align-items: center;
    position: relative;
  }

  &__checkbox {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    margin-right: 5px;
    flex-shrink: 0;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__image {
    width: 75px;
    height: 75px;
    border-radius: @radius-4;
    margin-right: 10px;
    flex-shrink: 0;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__info {
    flex: 1;
    overflow: hidden;
    min-width: 0;
  }

  &__goods-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 5px;
    gap: 10px;
  }

  &__name {
    flex: 1;
    height: 16px;
    border-radius: 4px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__quantity {
    width: 25px;
    height: 16px;
    border-radius: 4px;
    flex-shrink: 0;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__tags {
    display: flex;
    gap: 5px;
    margin-bottom: 5px;
  }

  &__spec-tag {
    width: 40px;
    height: 17px;
    border-radius: @radius-2;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__price {
    width: 60px;
    height: 18px;
    border-radius: 4px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}
</style>
